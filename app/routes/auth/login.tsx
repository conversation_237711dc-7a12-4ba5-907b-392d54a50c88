import type { CheckedState } from '@radix-ui/react-checkbox';
import { Label } from '@radix-ui/react-label';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import beasyIcon from '@/asset/images/beasy-icon.png'; // Adjust the path as necessary
// import { useAuth } from '@/hooks/useAuth';
import FormInput from '@/components/ui/common/FormInput';
import { useForm } from 'react-hook-form';
import { signInSchema, type SignInSchemaProps } from '@/lib/schema/signin-schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';

// ... other imports

function LoginPage() {
  const [rememberMe, setRememberMe] = useState<CheckedState>(false);
  const signinForm = useForm<SignInSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: ''
    }
  });

  // const { login, loginLoading, loginError } = useAuth();

  const onSubmit = (values: SignInSchemaProps) => {
    // Handle login logic here (e.g., API call)
    // login({ email, password });
    console.log(values);
    // console.log('Email:', email, 'Password:', password, 'Remember Me:', rememberMe);
  };

  return (
    <div className="min-h-screen grid grid-cols-2">
      {/* Left Image Section */}
      <div className="col-span-1 items-center justify-center flex">
        <img
          src="https://camo.githubusercontent.com/c2fd2f94aa55544327fc8ed8901aedb2eec8e3535243452b43646eb8086efe1a/68747470733a2f2f796176757a63656c696b65722e6769746875622e696f2f73616d706c652d696d616765732f696d6167652d34342e6a7067"
          alt="Description of image"
          className="object-cover w-[584px] h-[784px]"
        />
      </div>

      {/* Right Login Form Section */}
      <div className="flex items-center justify-center p-8">
        <div className="w-[400px]">
          <img src={beasyIcon} alt="Logo" className="w-16 h-16 mx-auto" />
          <p className="text-2xl font-bold mt-4 mb-10 text-center">Log in to your Account</p>

          <Form {...signinForm}>
            <form onSubmit={signinForm.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid w-full items-center gap-4">
                <div className="flex flex-col space-y-1.5">
                  <FormInput
                    control={signinForm.control}
                    name="email"
                    label="Email"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="flex flex-col space-y-1.5 relative">
                  <FormInput
                    control={signinForm.control}
                    name="password"
                    label="Password"
                    placeholder="Enter your password"
                    type="password"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rememberMe"
                      checked={rememberMe}
                      onCheckedChange={setRememberMe}
                    />
                    <Label htmlFor="rememberMe">Remember me</Label>
                  </div>
                  <Button variant="link" className="px-0">
                    Forgot Password
                  </Button>
                </div>
                <Button type="submit" className="w-full mt-2">
                  Login
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
