import { type RouteConfig, index, layout, route } from '@react-router/dev/routes';

export default [
  layout('./protected-layout.tsx', [
    layout('routes/dashboard-layout.tsx', [
      index('routes/dashboard.tsx'),
      route('user', 'routes/user.tsx'),
      route('customer', 'routes/customer.tsx'),

      // Voucher routes
      route('voucher', 'routes/voucher/voucher.tsx'),
      route('voucher/new-voucher', 'routes/voucher/new-voucher.tsx'),
      route('voucher/:voucherId', 'routes/voucher/edit-voucher.tsx'),

      // Banner routes
      route('banner', 'routes/banner.tsx'),

      route('service-bundle', 'routes/service-bundle.tsx'),
      route('audit-log', 'routes/audit-log.tsx'),
      route('top-up', 'routes/top-up.tsx'),
      route('referral-program', 'routes/referral-program.tsx'),
      route('notifications', 'routes/notifications.tsx')
    ]),
    layout('routes/order/layout.tsx', [route('order', 'routes/order/order.tsx')])
  ]),
  route('login', 'routes/auth/login.tsx')
] satisfies RouteConfig;
