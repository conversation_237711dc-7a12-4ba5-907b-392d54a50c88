const DASHBOARD_TITLE: DashboardTitleProps = {
  '/': 'Dashboard',
  '/order': 'Order',
  '/user': 'User',
  '/customer': 'Customer',
  '/voucher': 'Voucher',
  '/voucher/new-voucher': 'New Voucher',
  '/voucher/:id': 'Edit Voucher',
  '/banner': 'Banner',
  '/service-bundle': 'Service Bundle',
  '/audit-log': 'Audit Log',
  '/top-up': 'Top Up',
  '/referral-program': 'Referral Program',
  '/notifications': 'Notifications'
};

export const getPageTitle = (path: string): string => {
  if (DASHBOARD_TITLE[path]) return DASHBOARD_TITLE[path];

  // use a loop to check for dynamic paths for titles
  for (const key of Object.keys(DASHBOARD_TITLE)) {
    if (path.startsWith(key + '/')) {
      return DASHBOARD_TITLE[`${key}/:id`];
    }
  }

  return path;
};
