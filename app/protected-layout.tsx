import { Navigate, Outlet, useLocation } from 'react-router';
import { useAuth } from './hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = () => {
  const { user, isLoadingAuth } = useAuth(); // Get user and loading state from useAuth hook
  const location = useLocation();

  if (isLoadingAuth) {
    // return <div>Loading authentication...</div>;
    return <div />;
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
