import useAuthStore from '@/store/authStore';
import { Avatar, AvatarFallback, AvatarImage } from '../avatar';
import { Bell } from 'lucide-react';
import { Button } from '../button';

export default function HeaderRight() {
  const { user } = useAuthStore();

  const handleNavigationClick = () => {};

  return (
    <div className="flex flex-row items-center gap-6">
      <Button variant="ghost" size="icon" className="relative" onClick={handleNavigationClick}>
        <Bell className="!size-6" />
        <div className="rounded-full bg-red-600 text-white text-xs font-semibold w-4 h-4 absolute top-1 right-1">
          2
        </div>
      </Button>
      <div className="w-[2px] h-10 bg-border" />
      <div className="flex flex-row gap-3 justify-center items-center">
        <Avatar className="rounded-sm size-10">
          <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
        <div>
          <p className="font-semibold">{user?.name}</p>
          <p>{user?.role}</p>
        </div>
      </div>
    </div>
  );
}
