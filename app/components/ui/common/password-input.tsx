import * as React from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { Input } from '../input';
import { cn } from '@/lib/utils';
import { Button } from '../button';

export type PasswordInputProps = React.InputHTMLAttributes<HTMLInputElement> & {
  className?: string;
  disabled?: boolean;
};

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    return (
      <div className="relative">
        <Input
          type={showPassword ? 'text' : 'password'}
          className={cn('pr-10', className)} // Add padding-right for the icon
          ref={ref}
          {...props}
        />
        <Button
          type="button" // Important: Prevent this button from submitting the form
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={() => setShowPassword((prev) => !prev)}
          disabled={props.disabled} // Inherit disabled state from input
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4" aria-hidden="true" />
          ) : (
            <Eye className="h-4 w-4" aria-hidden="true" />
          )}
          <span className="sr-only">{showPassword ? 'Hide password' : 'Show password'}</span>
        </Button>
      </div>
    );
  }
);
PasswordInput.displayName = 'PasswordInput';

export { PasswordInput };
