import { Calendar } from 'lucide-react';
import { Button } from '../button';
import { Calendar as CalendarComponent } from '../calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../popover';
import { format } from 'date-fns';

export type DateRangePickerProps = {
  dateRange: { from?: Date; to?: Date };
  setDateRange: (range: { from?: Date; to?: Date }) => void;
  isCalendarOpen: boolean;
  setIsCalendarOpen: (open: boolean) => void;
};

export default function DateRangePicker({
  dateRange,
  setDateRange,
  isCalendarOpen,
  setIsCalendarOpen
}: DateRangePickerProps) {
  return (
    <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
      <PopoverTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          className="gap-2 bg-transparent min-w-[240px] justify-start">
          <Calendar className="h-4 w-4" />
          {dateRange.from && dateRange.to
            ? `${format(dateRange.from, 'dd MMM, yyyy')} - ${format(dateRange.to, 'dd MMM, yyyy')}`
            : 'Select date range'}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <CalendarComponent
          mode="range"
          selected={{
            from: dateRange.from,
            to: dateRange.to
          }}
          onSelect={(range) => {
            if (range) {
              setDateRange({
                from: range.from,
                to: range.to
              });
              // Close popover when both dates are selected
              if (range.from && range.to && range.from !== range.to) {
                setIsCalendarOpen(false);
              }
            }
          }}
          numberOfMonths={2}
          className="rounded-md border"
        />
      </PopoverContent>
    </Popover>
  );
}
