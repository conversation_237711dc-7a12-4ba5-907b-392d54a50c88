export const userData: UserManagementProps[] = [
  {
    id: '0001',
    name: '<PERSON>',
    role: 'User',
    phoneNumber: '**************',
    status: 'Active',
    date: '01 Jan, 2025'
  },
  {
    id: '0002',
    name: '<PERSON>',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '01 Jan, 2025'
  },
  {
    id: '0003',
    name: '<PERSON>',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '02 Jan, 2025'
  },
  {
    id: '0004',
    name: '<PERSON>',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0005',
    name: '<PERSON>',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0006',
    name: '<PERSON>',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0007',
    name: '<PERSON><PERSON>',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0008',
    name: 'Floyd Ullrich',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0009',
    name: 'Drew Hettinger',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0010',
    name: 'Christian Blick MD',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0011',
    name: 'Ryan Moen',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0012',
    name: 'Brent Morar I',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0013',
    name: 'Christy Bins',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0014',
    name: 'Marsha Monahan',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0015',
    name: 'Curtis Breitenberg',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0016',
    name: 'Lori Nader',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0017',
    name: 'Tomasa Stiedemann',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '03 Jan, 2025'
  },
  {
    id: '0018',
    name: 'Jerrod Mohr',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0019',
    name: 'Reina Tillman',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0020',
    name: 'Allan Schuppe',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0021',
    name: 'Irving Bosco',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '04 Jan, 2025'
  },
  {
    id: '0022',
    name: 'Lia Schulist',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0023',
    name: 'Wilfredo Cummerata',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0024',
    name: 'Domenic Hilpert',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0025',
    name: 'Clifton Ledner',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0026',
    name: 'Alene Champlin',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0027',
    name: 'Junior Bechtelar',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '05 Jan, 2025'
  },
  {
    id: '0028',
    name: "Wilma O'Hara",
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0029',
    name: 'Matilde Zieme',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0030',
    name: 'Kiara Gulgowski',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0031',
    name: 'Dewey Smitham',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0032',
    name: 'Stuart Hermann',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '06 Jan, 2025'
  },
  {
    id: '0033',
    name: 'Elsa Lockman',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0034',
    name: 'Leola Rogahn',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0035',
    name: 'Darrick Ritchie',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0036',
    name: 'Ora Leffler',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0037',
    name: 'Toni Lakin',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0038',
    name: "Alfredo O'Keefe",
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '07 Jan, 2025'
  },
  {
    id: '0039',
    name: 'Traci Kuhic',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0040',
    name: 'Nickolas Barton',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0041',
    name: 'Jaqueline Emard',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0042',
    name: 'Margarito Waelchi',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0043',
    name: 'Mariano Feest',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0044',
    name: 'Luann Collins',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0045',
    name: 'Francisca Emmerich',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '08 Jan, 2025'
  },
  {
    id: '0046',
    name: 'Lenny Bernier',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0047',
    name: 'Judy Kautzer',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0048',
    name: 'Rosalva Doyle',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0049',
    name: 'Edna Rice',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0050',
    name: 'Rufus Johns',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  }
];
