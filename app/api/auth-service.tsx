// // src/api/authService.ts

// const TOKEN_KEY = 'my_auth_token';
// const USER_KEY = 'my_auth_user';

// interface User {
//   id: string;
//   username: string;
//   email: string;
// }

// interface LoginResponse {
//   token: string;
//   user: User;
// }

// // Simulate an API call for login
// export const loginApi = async (username: string, password: string): Promise<LoginResponse> => {
//   return new Promise((resolve, reject) => {
//     setTimeout(() => {
//       if (username === 'user' && password === 'password') {
//         const user: User = { id: '123', username: 'user', email: '<EMAIL>' };
//         const token = 'fake-jwt-token-12345'; // Replace with a real JWT in production
//         localStorage.setItem(TOKEN_KEY, token);
//         localStorage.setItem(USER_KEY, JSON.stringify(user));
//         resolve({ token, user });
//       } else {
//         reject(new Error('Invalid credentials'));
//       }
//     }, 500);
//   });
// };

// // Simulate an API call for logout (optional, often just client-side token removal)
// export const logoutApi = async (): Promise<void> => {
//   return new Promise((resolve) => {
//     setTimeout(() => {
//       localStorage.removeItem(TOKEN_KEY);
//       localStorage.removeItem(USER_KEY);
//       resolve();
//     }, 200);
//   });
// };

// export const getStoredToken = (): string | null => {
//   return localStorage.getItem(TOKEN_KEY);
// };

// export const getStoredUser = (): User | null => {
//   const userJson = localStorage.getItem(USER_KEY);
//   return userJson ? JSON.parse(userJson) : null;
// };
