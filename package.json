{"name": "my-app", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "prepare": "husky", "lint": "eslint .", "lint:fix": "eslint --fix", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "immer": "^10.1.1", "isbot": "^5.1.27", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "moment": "^2.30.1", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-router": "^7.5.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.30.1", "@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^6.3.3", "vite-plugin-eslint": "^1.8.1", "vite-tsconfig-paths": "^5.1.4"}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --quiet --fix"], "*.{json,js,ts,jsx,tsx}": ["yarn format"]}}